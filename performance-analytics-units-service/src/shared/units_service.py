# Copyright (c) 2021, Syskron GmbH. All rights reserved.

import json
from http import HTT<PERSON>tatus
from typing import Any, Dict

from aws_lambda_powertools import Logger
from fastapi import HTTPException
from lib_cloud_sdk.api_gateway.responses import build_response
from performance_analytics.utility.block_counters import get_block_level_counters, query_counters
from performance_analytics.models.block_configuration import BlockCounterStatus

from .models import CounterReportResponse

LOGGER = Logger()

Response = dict[str, Any]


def get_units_report(
    time_from: int, time_to: int, machine_id: str, line_id: str, account: str, line_kpi: int
) -> CounterReportResponse:
    """Get units report based on line_kpi parameter.
    
    Args:
        time_from: Start timestamp in milliseconds
        time_to: End timestamp in milliseconds  
        machine_id: Machine identifier
        line_id: Line identifier
        account: Customer account
        line_kpi: Flag indicating whether to use line-level KPIs (1) or machine-level (0)
        
    Returns:
        CounterReportResponse: The units report data
    """
    base_response = {
        "customer": account,
        "eq_id": machine_id,
        "time_from": time_from,
        "time_to": time_to,
    }

    if int(line_kpi) == 1:
        return _get_block_counter_report(
            base_response, line_id, machine_id, account, time_from, time_to
        )
    return _get_machine_counter_report(base_response, machine_id, account, time_from, time_to)


def _get_block_counter_report(
    base_response: dict, line_id: str, machine_id: str, account: str, time_from: int, time_to: int
) -> CounterReportResponse:
    """Get units report using block counters.
    
    Args:
        base_response: Base response dictionary with common fields
        line_id: Line identifier
        machine_id: Machine identifier
        account: Customer account
        time_from: Start timestamp
        time_to: End timestamp
        
    Returns:
        CounterReportResponse: The units report data
    """
    block_counter_config = get_block_level_counters(
        account=account, line_id=line_id, time_from=time_from, time_to=time_to
    )

    response = base_response.copy()

    if block_counter_config.status in (
        BlockCounterStatus.SUCCESS,
        BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER,
    ):
        start_time = time_from
        if (
            block_counter_config.status
            == BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER
        ):
            start_time = block_counter_config.block_valid_from
        units_map = query_counters(
            account=account,
            machine_id=machine_id,
            time_from=start_time,
            time_to=time_to,
            calculate_block_counters=True,
        )
        for unit_type in ["units_produced", "units_defect", "units_total"]:
            response[unit_type] = units_map.get(unit_type, 0)
        if (
            block_counter_config.status
            == BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER
        ):
            response["time_from"] = start_time
            response["eq_id"] = line_id
    else:
        # For all other status flags, use machine counters as fallback
        machine_response = _get_machine_counter_report(
            base_response, response["eq_id"], account, time_from, time_to
        )
        response.update(machine_response.dict())

    return CounterReportResponse(**response)


def _get_machine_counter_report(
    base_response: dict, machine_id: str, account: str, time_from: int, time_to: int
) -> CounterReportResponse:
    """Get units report using machine counters.
    
    Args:
        base_response: Base response dictionary with common fields
        machine_id: Machine identifier
        account: Customer account
        time_from: Start timestamp
        time_to: End timestamp
        
    Returns:
        CounterReportResponse: The units report data
    """
    response = base_response.copy()

    try:
        # Call the query_counters function which returns a dictionary
        units_map = query_counters(account, machine_id, time_from, time_to)

        # Update the response with the units data
        response.update(
            {
                "units_produced": round(units_map["units_produced"]),
                "units_defect": round(units_map["units_defect"]),
                "units_total": round(units_map["units_total"]),
            }
        )

        # Add station_counters if they exist
        if "station_counters" in units_map:
            response["station_counters"] = units_map["station_counters"]

    except HTTPException as e:
        LOGGER.debug("HTTP error getting counter data for machine_id=%s: %s", machine_id, str(e))
        # Set default values for units in case of error
        response.update({"units_produced": 0, "units_defect": 0, "units_total": 0})

    return CounterReportResponse(**response)
