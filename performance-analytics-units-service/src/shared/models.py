# Copyright (c) 2021, Syskron GmbH. All rights reserved.

from typing import Any, Dict, Optional
from pydantic import BaseModel, Field


class CounterReportResponse(BaseModel):
    """Response model for counter report endpoint."""
    
    customer: str = Field(..., description="Customer account identifier")
    eq_id: str = Field(..., description="Equipment ID (machine or line ID)")
    time_from: int = Field(..., description="Start timestamp in milliseconds")
    time_to: int = Field(..., description="End timestamp in milliseconds")
    units_produced: int = Field(default=0, description="Number of units produced")
    units_defect: int = Field(default=0, description="Number of defective units")
    units_total: int = Field(default=0, description="Total number of units")
    station_counters: Optional[Dict[str, Any]] = Field(default=None, description="Station-specific counter data")

    class Config:
        """Pydantic model configuration."""
        json_encoders = {
            # Ensure integers are properly serialized
            int: lambda v: v if v is not None else 0
        }
        # Allow extra fields for backward compatibility
        extra = "allow"
