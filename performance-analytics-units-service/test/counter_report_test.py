# Copyright (c) 2021, Syskron GmbH. All rights reserved.

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient

from api.lambda_function import app


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_s2a_properties():
    """Mock Share2ActProperties dependency."""
    mock_props = Mock()
    mock_props.account = "test-account"
    return mock_props


@pytest.fixture
def mock_units_service():
    """Mock the units service."""
    with patch("api.endpoints.counter_report.get_units_report") as mock:
        mock_response = Mock()
        mock_response.customer = "test-account"
        mock_response.eq_id = "test-machine"
        mock_response.time_from = *************
        mock_response.time_to = *************
        mock_response.units_produced = 100
        mock_response.units_defect = 5
        mock_response.units_total = 105
        mock_response.station_counters = None
        mock.return_value = mock_response
        yield mock


def test_counter_report_endpoint_success(client, mock_units_service):
    """Test successful counter report request."""
    with patch("api.endpoints.counter_report.Share2ActProperties") as mock_dep:
        mock_dep.return_value.account = "test-account"
        
        response = client.get(
            "/v1/performance-analytics/counter-report/v1/test-line/test-machine",
            params={
                "time_from": "*************",
                "time_to": "*************",
                "line_kpi": "0"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["customer"] == "test-account"
        assert data["eq_id"] == "test-machine"
        assert data["units_produced"] == 100
        assert data["units_defect"] == 5
        assert data["units_total"] == 105


def test_counter_report_endpoint_missing_time_to(client, mock_units_service):
    """Test counter report request with missing time_to parameter."""
    with patch("api.endpoints.counter_report.Share2ActProperties") as mock_dep:
        mock_dep.return_value.account = "test-account"
        
        with patch("time.time", return_value=**********.530):
            response = client.get(
                "/v1/performance-analytics/counter-report/v1/test-line/test-machine",
                params={
                    "time_from": "*************",
                    "line_kpi": "0"
                }
            )
            
            assert response.status_code == 200
            # Verify that get_units_report was called with current timestamp as time_to
            mock_units_service.assert_called_once()
            call_args = mock_units_service.call_args
            assert call_args.kwargs["time_to"] == *************


def test_counter_report_endpoint_line_kpi_flag(client, mock_units_service):
    """Test counter report request with line_kpi=1."""
    with patch("api.endpoints.counter_report.Share2ActProperties") as mock_dep:
        mock_dep.return_value.account = "test-account"
        
        response = client.get(
            "/v1/performance-analytics/counter-report/v1/test-line/test-machine",
            params={
                "time_from": "*************",
                "time_to": "*************",
                "line_kpi": "1"
            }
        )
        
        assert response.status_code == 200
        # Verify that get_units_report was called with line_kpi=1
        mock_units_service.assert_called_once()
        call_args = mock_units_service.call_args
        assert call_args.kwargs["line_kpi"] == 1
